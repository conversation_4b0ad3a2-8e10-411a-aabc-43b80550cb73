# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Vue 3 + TypeScript + Vite 的手机端 H5 技术调研项目，主要用于展示 ECharts 图表功能。项目采用纯前端架构，所有数据通过 mock 生成，无需外部接口。

## 开发命令

### 启动开发服务器
```bash
npm run dev
# 服务器将在 http://localhost:3000 启动，支持热重载
```

### 构建项目
```bash
npm run build
# 构建输出到 dist/ 目录
```

### 代码规范检查
```bash
npm run lint
# 自动修复 ESLint 问题
npm run format
# 格式化代码
```

### 预览构建结果
```bash
npm run preview
# 预览生产构建
```

## 项目架构

### 核心技术栈
- **Vue 3**: 使用 Composition API 和 `<script setup>` 语法
- **TypeScript**: 全项目类型安全
- **Vite**: 构建工具，配置了路径别名 `@` 指向 `src/`
- **ECharts 5.4.3**: 图表库，通过全量导入使用
- **Vue Router 4**: 客户端路由

### 目录结构说明
```
src/
├── components/     # 可复用组件
│   └── BarChart.vue    # ECharts 柱状图组件
├── views/         # 页面组件  
│   └── ChartView.vue   # 主要图表展示页面
├── mock/          # 数据模拟层
│   └── chartData.ts    # 图表数据生成器
├── router/        # 路由配置
├── assets/        # 静态资源和全局样式
└── utils/         # 工具函数（预留）
```

### 关键组件架构

**BarChart.vue 组件**:
- 封装 ECharts 初始化和生命周期管理
- 支持响应式数据更新和窗口大小调整
- 接受 `BarChartData` 类型的 props
- 自动处理 ECharts 实例的创建和销毁

**数据 Mock 层**:
- `chartData.ts` 提供类型定义和数据生成函数
- `generateBarChartData()` 生成多系列柱状图数据
- `generateSimpleBarData()` 生成简单柱状图数据
- 所有数据都是随机生成，适合技术调研和演示

### 移动端优化
- viewport 配置禁用缩放和电话号码识别
- CSS 采用响应式设计，在 768px 以下调整字体大小
- 图表组件支持触摸交互
- 按钮和交互元素针对手机端优化

### 开发规范
- 使用 `<AUTHOR> 和当前日期标注函数注释
- 组件使用 `@Setter/@Getter` 而不是 `@Data`
- 单个文件不超过 1000 行
- 严格遵循 TypeScript 类型检查
- CSS 使用 scoped 样式避免污染

## 扩展指南

添加新图表类型时：
1. 在 `mock/chartData.ts` 中定义新的数据类型和生成函数
2. 在 `components/` 中创建对应的图表组件
3. 在 `views/` 中创建或更新页面来展示新图表
4. 更新路由配置（如需要）

ECharts 相关操作：
- 图表实例通过 `echarts.init()` 创建
- 配置通过 `setOption()` 设置，第二个参数设为 `true` 进行完全替换
- 组件卸载时必须调用 `dispose()` 释放资源
- 窗口大小变化时调用 `resize()` 方法