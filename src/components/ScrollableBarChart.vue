<template>
  <div class="scrollable-bar-chart">
    <div ref="chartRef" class="chart-wrapper"></div>
    
    <!-- 滑动提示 -->
    <div v-if="showScrollHint" class="scroll-hint">
      <span class="hint-text">👆 可左右滑动查看更多数据</span>
    </div>
    
    <!-- 滑动指示器 -->
    <div class="scroll-indicator">
      <div 
        class="indicator-bar"
        :style="{ width: indicatorWidth + '%', transform: `translateX(${indicatorPosition}px)` }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import type { BarChartData } from '@/mock/chartData'

interface Props {
  data: BarChartData
  height?: string
  showScrollHint?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  height: '350px',
  showScrollHint: true
})

const chartRef = ref<HTMLElement>()
const indicatorWidth = ref(30)
const indicatorPosition = ref(0)

let chartInstance: echarts.ECharts | null = null
let startIndex = 0
const visibleCount = 8 // 同时显示的数据点数量

/**
 * 初始化图表
 * <AUTHOR>  
 * @date 2025-08-18 16:46:09
 */
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
  
  // 添加触摸和鼠标滑动事件
  addScrollEvents()
}

/**
 * 更新图表配置
 */
const updateChart = () => {
  if (!chartInstance || !props.data.categories.length) return
  
  const endIndex = Math.min(startIndex + visibleCount, props.data.categories.length)
  const visibleCategories = props.data.categories.slice(startIndex, endIndex)
  const visibleSeries = props.data.series.map(series => ({
    ...series,
    data: series.data.slice(startIndex, endIndex)
  }))
  
  const option = {
    title: {
      text: props.data.title,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      borderRadius: 8,
      textStyle: {
        color: '#fff'
      },
      // 添加弹性动画
      animation: true,
      animationDuration: 200,
      animationEasing: 'elasticOut'
    },
    legend: {
      bottom: '8%',
      data: visibleSeries.map(item => item.name),
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      top: '20%',
      left: '5%',
      right: '5%',
      bottom: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: visibleCategories,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        fontSize: 11,
        rotate: visibleCategories.length > 6 ? 45 : 0,
        margin: 10
      },
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 11
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    // 全局动画配置
    animation: true,
    animationDuration: 800,
    animationEasing: 'elasticOut',
    animationDelay: (idx: number) => idx * 100,
    
    series: visibleSeries.map((item, index) => ({
      name: item.name,
      type: 'bar',
      data: item.data,
      barWidth: '18%',
      itemStyle: {
        color: getSeriesColor(index),
        borderRadius: [4, 4, 0, 0],
        // 添加光泽效果
        opacity: 0.9
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 15,
          shadowOffsetX: 0,
          shadowOffsetY: 5,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          opacity: 1,
          borderWidth: 2,
          borderColor: '#fff'
        },
        // 强调时的弹性动画
        animationDuration: 300,
        animationEasing: 'elasticOut'
      },
      // 柱子出现动画
      animationDelay: (idx: number) => (index * visibleCount + idx) * 80
    }))
  }
  
  chartInstance.setOption(option, true)
  updateScrollIndicator()
}

/**
 * 获取系列颜色
 */
const getSeriesColor = (index: number): string => {
  const colors = [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  ]
  return colors[index % colors.length]
}

/**
 * 添加滑动事件
 */
const addScrollEvents = () => {
  if (!chartRef.value) return
  
  let startX = 0
  let isDragging = false
  
  // 触摸事件
  chartRef.value.addEventListener('touchstart', (e) => {
    startX = e.touches[0].clientX
    isDragging = true
  }, { passive: false })
  
  chartRef.value.addEventListener('touchmove', (e) => {
    if (!isDragging) return
    e.preventDefault()
    
    const deltaX = startX - e.touches[0].clientX
    if (Math.abs(deltaX) > 50) {
      if (deltaX > 0 && startIndex < props.data.categories.length - visibleCount) {
        // 向左滑动，显示右侧数据
        startIndex = Math.min(startIndex + 1, props.data.categories.length - visibleCount)
        updateChart()
      } else if (deltaX < 0 && startIndex > 0) {
        // 向右滑动，显示左侧数据
        startIndex = Math.max(startIndex - 1, 0)
        updateChart()
      }
      startX = e.touches[0].clientX
    }
  }, { passive: false })
  
  chartRef.value.addEventListener('touchend', () => {
    isDragging = false
  })
  
  // 鼠标滚轮事件
  chartRef.value.addEventListener('wheel', (e) => {
    e.preventDefault()
    
    if (e.deltaX > 0 && startIndex < props.data.categories.length - visibleCount) {
      startIndex = Math.min(startIndex + 1, props.data.categories.length - visibleCount)
      updateChart()
    } else if (e.deltaX < 0 && startIndex > 0) {
      startIndex = Math.max(startIndex - 1, 0)
      updateChart()
    }
  })
}

/**
 * 更新滑动指示器
 */
const updateScrollIndicator = () => {
  const totalData = props.data.categories.length
  const progress = totalData > visibleCount ? startIndex / (totalData - visibleCount) : 0
  
  indicatorWidth.value = (visibleCount / totalData) * 100
  indicatorPosition.value = progress * (100 - indicatorWidth.value) * 2.5
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 监听数据变化
watch(() => props.data, () => {
  startIndex = 0
  updateChart()
}, { deep: true })
</script>

<style scoped>
.scrollable-bar-chart {
  width: 100%;
  height: v-bind('props.height');
  position: relative;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-wrapper {
  width: 100%;
  height: calc(100% - 40px);
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
}

.chart-wrapper:active {
  cursor: grabbing;
}

.scroll-hint {
  position: absolute;
  top: 10px;
  right: 15px;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  animation: fadeInOut 3s ease-in-out infinite;
  backdrop-filter: blur(10px);
}

.hint-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

.scroll-indicator {
  position: absolute;
  bottom: 8px;
  left: 15px;
  right: 15px;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.indicator-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

@keyframes fadeInOut {
  0%, 100% { 
    opacity: 0.7; 
    transform: translateY(0);
  }
  50% { 
    opacity: 1; 
    transform: translateY(-2px);
  }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .scroll-hint {
    top: 8px;
    right: 10px;
    padding: 4px 10px;
    font-size: 11px;
  }
  
  .scroll-indicator {
    bottom: 6px;
    left: 10px;
    right: 10px;
    height: 3px;
  }
}

/* 添加触摸反馈 */
.scrollable-bar-chart:active {
  transform: scale(0.995);
  transition: transform 0.1s ease;
}
</style>