<template>
  <div class="scrollable-chart-container">
    <div class="chart-header">
      <h3>{{ title }}</h3>
    </div>
    <div class="chart-scroll-wrapper" ref="scrollWrapper">
      <div class="chart-content" ref="chartContent" :style="{ width: contentWidth + 'px' }">
        <div class="chart-bars">
          <div
            v-for="(item, index) in chartData"
            :key="index"
            class="bar-item"
            :class="{ 'visible': visibleBars.includes(index) }"
            ref="barItems"
          >
            <div class="bar-column">
              <div class="bar-value">{{ item.value }}</div>
              <div class="bar-visual" :style="getBarStyle(item, index)"></div>
            </div>
            <div class="bar-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'

/**
 * 可滑动柱状图数据项接口
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
interface ScrollableBarItem {
  label: string
  value: number
  color?: string
}

/**
 * 组件属性接口
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
interface Props {
  title?: string
  data: ScrollableBarItem[]
  barWidth?: number
  barGap?: number
  maxBarHeight?: number
  colors?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  title: '柱状图',
  barWidth: 60,
  barGap: 20,
  maxBarHeight: 200,
  colors: () => ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4']
})

const scrollWrapper = ref<HTMLElement>()
const chartContent = ref<HTMLElement>()
const barItems = ref<HTMLElement[]>([])
const visibleBars = ref<number[]>([])

/**
 * 计算图表内容宽度
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const contentWidth = computed(() => {
  return props.data.length * (props.barWidth + props.barGap) + props.barGap
})

/**
 * 获取最大值用于计算比例
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const maxValue = computed(() => {
  return Math.max(...props.data.map(item => item.value))
})

/**
 * 处理后的图表数据
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const chartData = computed(() => {
  return props.data.map((item, index) => ({
    ...item,
    color: item.color || props.colors[index % props.colors.length]
  }))
})

/**
 * 获取柱子样式
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const getBarStyle = (item: ScrollableBarItem & { color: string }, index: number) => {
  const height = (item.value / maxValue.value) * props.maxBarHeight
  const isVisible = visibleBars.value.includes(index)
  
  return {
    height: `${height}px`,
    backgroundColor: item.color,
    width: `${props.barWidth}px`,
    transform: isVisible ? 'scaleY(1)' : 'scaleY(0)',
    opacity: isVisible ? 1 : 0
  }
}

/**
 * 检查哪些柱子在可视区域内
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const checkVisibleBars = () => {
  if (!scrollWrapper.value || !barItems.value.length) return

  const scrollLeft = scrollWrapper.value.scrollLeft
  const containerWidth = scrollWrapper.value.clientWidth
  const scrollRight = scrollLeft + containerWidth

  const newVisibleBars: number[] = []

  barItems.value.forEach((barElement, index) => {
    if (!barElement) return

    const barLeft = barElement.offsetLeft
    const barRight = barLeft + barElement.offsetWidth
    
    // 柱子与可视区域有交集就认为可见
    if (barRight > scrollLeft - 50 && barLeft < scrollRight + 50) {
      newVisibleBars.push(index)
    }
  })

  // 批量更新可见柱子，添加延迟实现交错动画效果
  newVisibleBars.forEach((barIndex, i) => {
    if (!visibleBars.value.includes(barIndex)) {
      setTimeout(() => {
        if (!visibleBars.value.includes(barIndex)) {
          visibleBars.value.push(barIndex)
        }
      }, i * 100) // 每个柱子延迟100ms出现
    }
  })

  // 移除不可见的柱子
  visibleBars.value = visibleBars.value.filter(index => 
    newVisibleBars.includes(index) || 
    barItems.value[index]?.offsetLeft < scrollRight + 50
  )
}

/**
 * 滚动事件处理器
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const handleScroll = () => {
  checkVisibleBars()
}

/**
 * 初始化组件
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
onMounted(async () => {
  await nextTick()
  
  if (scrollWrapper.value) {
    scrollWrapper.value.addEventListener('scroll', handleScroll)
    // 初始检查可见柱子
    setTimeout(checkVisibleBars, 100)
  }
})

/**
 * 清理事件监听器
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
onUnmounted(() => {
  if (scrollWrapper.value) {
    scrollWrapper.value.removeEventListener('scroll', handleScroll)
  }
})

/**
 * 监听数据变化，重新检查可见性
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
watch(() => props.data, () => {
  visibleBars.value = []
  nextTick(() => {
    setTimeout(checkVisibleBars, 100)
  })
}, { deep: true })
</script>

<style scoped>
.scrollable-chart-container {
  width: 100%;
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.chart-scroll-wrapper {
  overflow-x: auto;
  overflow-y: hidden;
  width: 100%;
  height: 280px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 隐藏滚动条但保持滚动功能 */
.chart-scroll-wrapper::-webkit-scrollbar {
  display: none;
}

.chart-content {
  height: 100%;
  position: relative;
  min-width: 100%;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  height: 240px;
  padding: 0 10px;
  gap: 0;
}

.bar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.bar-column {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 220px;
  justify-content: flex-end;
  position: relative;
}

.bar-value {
  position: absolute;
  top: -25px;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.bar-visual {
  border-radius: 4px 4px 0 0;
  transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform-origin: bottom;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* 柱子出现动画 */
.bar-item:not(.visible) .bar-visual {
  transform: scaleY(0);
  opacity: 0;
}

.bar-item.visible .bar-visual {
  transform: scaleY(1);
  opacity: 1;
}

/* 回弹效果 */
.bar-visual::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.3)
  );
  border-radius: 4px 4px 0 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bar-item.visible .bar-visual:hover::after {
  opacity: 1;
}

.bar-label {
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
  min-height: 16px;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .scrollable-chart-container {
    padding: 16px;
  }
  
  .chart-header h3 {
    font-size: 16px;
  }
  
  .bar-value {
    font-size: 11px;
  }
  
  .bar-label {
    font-size: 11px;
  }
}

/* 滚动提示 */
.chart-scroll-wrapper::before {
  content: '👈 左右滑动查看更多';
  position: absolute;
  right: 20px;
  top: 10px;
  font-size: 12px;
  color: #9ca3af;
  z-index: 10;
  animation: fadeInOut 3s ease-in-out infinite;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}
</style>