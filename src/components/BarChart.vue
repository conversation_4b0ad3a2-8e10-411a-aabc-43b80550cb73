<template>
  <div class="bar-chart-component">
    <div ref="chartRef" class="chart-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import type { BarChartData } from '@/mock/chartData'

interface Props {
  data: BarChartData
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px'
})

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

/**
 * 初始化图表
 * <AUTHOR>
 * @date 2025-08-18 16:37:36
 */
const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
  
  // 添加数据缩放回弹效果的事件监听
  chartInstance.on('datazoom', (params: any) => {
    if (params.batch && params.batch[0]) {
      const zoom = params.batch[0]
      const { start, end } = zoom
      
      // 检查是否到达边界，如果是则添加回弹动画
      if (start <= 0 || end >= 100) {
        setTimeout(() => {
          if (chartInstance) {
            const newStart = Math.max(0, Math.min(start, 90))
            const newEnd = Math.min(100, Math.max(end, 10))
            
            chartInstance.dispatchAction({
              type: 'dataZoom',
              start: newStart,
              end: newEnd,
              animation: true,
              animationDuration: 400,
              animationEasing: 'elasticOut'
            })
          }
        }, 50)
      }
      
      // 为滑动时显示的柱子添加弹性出现动画
      setTimeout(() => {
        if (chartInstance) {
          const option = chartInstance.getOption()
          const series = option.series as any[]
          
          if (series && series[0] && series[0].type === 'bar') {
            // 重新设置柱子动画，让新出现的柱子有弹性效果
            const updatedSeries = series.map(s => ({
              ...s,
              animationDuration: 600,
              animationEasing: 'elasticOut',
              animationDelay: (idx: number) => {
                // 计算当前显示范围内的索引
                const totalCount = s.data ? s.data.length : 0
                const startIdx = Math.floor((start / 100) * totalCount)
                const relativeIdx = idx - startIdx
                return Math.max(0, relativeIdx * 25) // 相对位置的延迟
              }
            }))
            
            chartInstance.setOption({
              series: updatedSeries
            }, false, true) // 不合并，静默更新
          }
        }
      }, 100)
    }
  })
  
  // 监听数据缩放开始事件
  chartInstance.on('dataZoomStarted', () => {
    if (chartInstance) {
      // 在缩放开始时添加过渡效果
      const option = chartInstance.getOption()
      chartInstance.setOption({
        animation: true,
        animationDuration: 300,
        animationEasing: 'cubicOut'
      }, false, true)
    }
  })
}

/**
 * 更新图表配置
 */
const updateChart = () => {
  if (!chartInstance) return
  
  // 判断是否为单系列多柱状图（如36个月的数据）
  const isMultiMonthBar = props.data.categories.length > 12 && props.data.series.length === 1
  // 判断是否为单个柱状图
  const isSingleBar = props.data.categories.length === 1 && props.data.series.length === 1
  
  const option = {
    // 全局动画配置
    animation: true,
    animationThreshold: 3000,
    animationDuration: isMultiMonthBar ? 1000 : 800,
    animationEasing: isMultiMonthBar ? 'elasticOut' : 'cubicOut',
    animationDelay: 0,
    animationDurationUpdate: isMultiMonthBar ? 600 : 400,
    animationEasingUpdate: 'elasticOut',
    animationDelayUpdate: 0,
    
    title: {
      text: props.data.title,
      left: 'center',
      textStyle: {
        fontSize: isSingleBar ? 18 : (isMultiMonthBar ? 16 : 16),
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      formatter: (params: any) => {
        if ((isSingleBar || isMultiMonthBar) && params.length > 0) {
          const data = params[0]
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${data.name}</div>
              <div style="color: ${data.color};">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${data.color}; border-radius: 50%; margin-right: 8px;"></span>
                ${data.seriesName}: ${data.value}
              </div>
            </div>
          `
        }
        return null
      }
    },
    legend: (isSingleBar || isMultiMonthBar) ? undefined : {
      bottom: '5%',
      data: props.data.series.map(item => item.name)
    },
    grid: {
      top: (isSingleBar || isMultiMonthBar) ? '20%' : '15%',
      left: isMultiMonthBar ? '5%' : (isSingleBar ? '10%' : '3%'),
      right: isMultiMonthBar ? '5%' : (isSingleBar ? '10%' : '4%'),
      bottom: isMultiMonthBar ? '25%' : (isSingleBar ? '15%' : '15%'),
      containLabel: true
    },
    // 添加数据缩放组件，支持滑动查看
    dataZoom: isMultiMonthBar ? [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: 70, // 默认显示最后30%的数据（最近的月份）
        end: 100,
        bottom: '5%',
        height: 20,
        handleStyle: {
          color: '#4facfe',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        dataBackground: {
          areaStyle: {
            color: 'rgba(79, 172, 254, 0.1)'
          },
          lineStyle: {
            opacity: 0.5,
            color: '#4facfe'
          }
        },
        selectedDataBackground: {
          areaStyle: {
            color: 'rgba(79, 172, 254, 0.3)'
          }
        },
        textStyle: {
          color: '#666'
        },
        brushSelect: true,
        emphasis: {
          handleStyle: {
            shadowBlur: 8,
            color: '#00f2fe'
          }
        }
      },
      {
        type: 'inside',
        xAxisIndex: [0],
        start: 70,
        end: 100,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: false,
        preventDefaultMouseMove: true,
        // 添加回弹效果的关键配置
        zoomLock: false,
        rangeMode: ['percent', 'percent'],
        // 启用边界回弹
        orient: 'horizontal',
        minSpan: 10, // 最小显示范围10%
        maxSpan: 100, // 最大显示范围100%
        // 添加缓动效果
        animation: true,
        animationDuration: 400,
        animationEasing: 'elasticOut',
        // 触摸优化
        throttle: 100,
        sensitivity: 1
      }
    ] : undefined,
    xAxis: {
      type: 'category',
      data: props.data.categories,
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        fontSize: isMultiMonthBar ? 10 : (isSingleBar ? 14 : 12),
        color: '#666',
        fontWeight: isSingleBar ? 'bold' : 'normal',
        rotate: isMultiMonthBar ? 45 : 0, // 多月数据时旋转标签
        interval: isMultiMonthBar ? 'auto' : 0
      },
      axisLine: {
        lineStyle: {
          color: (isSingleBar || isMultiMonthBar) ? '#e0e0e0' : '#ccc'
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    series: props.data.series.map((item, seriesIndex) => {
      if (isSingleBar) {
        // 单柱样式
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          barWidth: '60%',
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#4facfe'
                },
                {
                  offset: 1,
                  color: '#00f2fe'
                }
              ]
            },
            borderRadius: [8, 8, 0, 0],
            shadowColor: 'rgba(79, 172, 254, 0.3)',
            shadowBlur: 10,
            shadowOffsetY: 5
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 20,
              shadowOffsetY: 8,
              shadowColor: 'rgba(79, 172, 254, 0.5)'
            }
          },
          label: {
            show: true,
            position: 'top',
            color: '#4facfe',
            fontSize: 16,
            fontWeight: 'bold',
            formatter: '{c}'
          },
          animationDuration: 1000,
          animationEasing: 'bounceOut'
        }
      } else if (isMultiMonthBar) {
        // 多月数据样式
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          barWidth: '80%',
          itemStyle: {
            color: (params: any) => {
              // 根据数据索引生成渐变色
              const colors = [
                ['#667eea', '#764ba2'],
                ['#f093fb', '#f5576c'],
                ['#4facfe', '#00f2fe'],
                ['#43e97b', '#38f9d7'],
                ['#fa709a', '#fee140']
              ]
              const colorSet = colors[Math.floor(params.dataIndex / 12) % colors.length]
              return {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: colorSet[0] },
                  { offset: 1, color: colorSet[1] }
                ]
              }
            },
            borderRadius: [3, 3, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 12,
              shadowOffsetY: 6,
              shadowColor: 'rgba(0, 0, 0, 0.4)',
              borderRadius: [4, 4, 0, 0]
            },
            // 悬停时也有弹性动画
            scale: true,
            scaleSize: 1.05
          },
          // 初始加载动画 - 依次弹性长出
          animation: true,
          animationThreshold: 2000,
          animationDuration: (idx: number) => {
            return 1200 + (idx % 36) * 50 // 每个柱子错开50ms，总共1.8秒内完成
          },
          animationEasing: 'elasticOut',
          animationDelay: (idx: number) => {
            return (idx % 36) * 40 // 每个柱子延迟40ms启动
          },
          // 数据更新时的动画
          animationDurationUpdate: 800,
          animationEasingUpdate: 'elasticOut',
          animationDelayUpdate: (idx: number) => {
            return (idx % 12) * 30 // 数据更新时按月份错开
          }
        }
      } else {
        // 普通多系列样式
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          itemStyle: {
            color: seriesIndex === 0 ? '#5470c6' : '#91cc75',
            borderRadius: [2, 2, 0, 0]
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      }
    })
  }
  
  chartInstance.setOption(option, true)
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(async () => {
  await nextTick()
  
  // 添加图表容器的入场动画
  if (chartRef.value) {
    chartRef.value.style.opacity = '0'
    chartRef.value.style.transform = 'translateY(20px)'
    chartRef.value.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)'
    
    setTimeout(() => {
      if (chartRef.value) {
        chartRef.value.style.opacity = '1'
        chartRef.value.style.transform = 'translateY(0)'
      }
    }, 100)
  }
  
  // 延迟初始化图表，让容器动画先完成
  setTimeout(() => {
    initChart()
  }, 200)
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  window.removeEventListener('resize', handleResize)
})

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })
</script>

<style scoped>
.bar-chart-component {
  width: 100%;
  height: v-bind('props.height');
}

.chart-wrapper {
  width: 100%;
  height: 100%;
}
</style>