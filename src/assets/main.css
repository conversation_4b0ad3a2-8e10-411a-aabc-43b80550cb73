/* CSS重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
  overflow-x: hidden;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  html {
    font-size: 12px;
  }
}

/* 通用样式 */
.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 15px;
}

.page-header {
  background: #fff;
  padding: 15px;
  border-bottom: 1px solid #e8e8e8;
  text-align: center;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-container {
  background: #fff;
  margin: 15px;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}