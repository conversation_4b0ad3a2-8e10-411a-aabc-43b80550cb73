<template>
  <div class="chart-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">ECharts 技术调研</h1>
    </div>
    
    <!-- 普通柱状图 -->
    <div class="chart-section">
      <h2 class="section-title">普通柱状图</h2>
      <div class="chart-container">
        <BarChart :data="chartData" height="350px" />
      </div>
      
      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn" @click="refreshData" :disabled="isLoading">
          {{ isLoading ? '刷新中...' : '刷新数据' }}
        </button>
      </div>
    </div>
    
    <!-- 单柱美化图表 -->
    <div class="chart-section">
      <h2 class="section-title">最近3年月度数据图表</h2>
      <div class="chart-container single-bar">
        <BarChart :data="singleBarData" height="450px" />
      </div>
      
      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn single-bar-btn" @click="refreshSingleBarData" :disabled="isSingleLoading">
          {{ isSingleLoading ? '刷新中...' : '刷新月度数据' }}
        </button>
      </div>
      
      <!-- 特性说明 -->
      <div class="feature-card">
        <h4>📊 图表特性</h4>
        <ul>
          <li>📅 <strong>时间跨度</strong>：显示最近3年共36个月的数据</li>
          <li>🎨 <strong>颜色分年</strong>：每年使用不同的渐变色区分</li>
          <li>🔄 <strong>数据缩放</strong>：底部滑动条可查看不同时间段</li>
          <li>📱 <strong>触摸滑动</strong>：支持手指拖拽查看历史数据</li>
          <li>🏷️ <strong>标签旋转</strong>：月份标签倾斜显示避免重叠</li>
        </ul>
      </div>
    </div>
    
    <!-- 可滑动柱状图 -->
    <div class="chart-section">
      <h2 class="section-title">可滑动柱状图</h2>
      <div class="chart-container scrollable">
        <ScrollableBarChart :data="scrollableData" height="380px" />
      </div>
      
      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn secondary" @click="refreshScrollableData" :disabled="isScrollLoading">
          {{ isScrollLoading ? '刷新中...' : '刷新滑动图表' }}
        </button>
      </div>
      
      <!-- 操作说明 -->
      <div class="instruction-card">
        <h4>💡 操作指南</h4>
        <ul>
          <li>📱 <strong>触摸滑动</strong>：手指左右滑动查看更多数据</li>
          <li>🖱️ <strong>鼠标操作</strong>：鼠标滚轮左右滑动</li>
          <li>📊 <strong>动画效果</strong>：点击柱子查看弹性动画</li>
          <li>📍 <strong>位置指示</strong>：底部指示条显示当前位置</li>
        </ul>
      </div>
    </div>
    
    <!-- 新的可滑动柱状图（自定义实现） -->
    <div class="chart-section">
      <h2 class="section-title">自定义滑动柱状图（回弹动画）</h2>
      <div class="chart-container custom-scrollable">
        <NewScrollableBarChart 
          :data="newScrollableData" 
          title="全国主要城市GDP数据" 
          :bar-width="70"
          :bar-gap="25"
          :max-bar-height="220"
        />
      </div>
      
      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn custom" @click="refreshNewScrollableData" :disabled="isNewScrollLoading">
          {{ isNewScrollLoading ? '数据生成中...' : '重新生成数据' }}
        </button>
      </div>
      
      <!-- 特性说明 -->
      <div class="feature-card custom">
        <h4>🚀 新特性展示</h4>
        <ul>
          <li>🎨 <strong>回弹动画</strong>：柱子出现时有弹性伸缩效果</li>
          <li>📱 <strong>横向滑动</strong>：原生滚动，流畅丝滑</li>
          <li>🎯 <strong>可视检测</strong>：只有进入可视区域的柱子才会出现</li>
          <li>⚡ <strong>交错动画</strong>：柱子按序列依次出现，更具视觉冲击</li>
          <li>💫 <strong>渐变光泽</strong>：hover时显示渐变光泽效果</li>
        </ul>
      </div>
    </div>
    
    <!-- 全新丝滑柱状图（手势交互+回弹效果） -->
    <div class="chart-section">
      <h2 class="section-title">🚀 终极丝滑柱状图（手势交互+回弹效果）</h2>
      <div class="chart-container ultimate-scrollable">
        <SmoothScrollBarChart 
          :data="smoothScrollData" 
          title="全国城市经济数据排行榜" 
          :bar-width="55"
          :bar-spacing="18"
          :max-bar-height="200"
        />
      </div>
      
      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn ultimate" @click="refreshNewScrollableData" :disabled="isNewScrollLoading">
          {{ isNewScrollLoading ? '数据生成中...' : '重新生成数据' }}
        </button>
      </div>
      
      <!-- 终极特性说明 -->
      <div class="feature-card ultimate">
        <h4>🏆 终极特性展示</h4>
        <ul>
          <li>🎯 <strong>精准手势识别</strong>：支持触摸、鼠标、滚轮多种交互方式</li>
          <li>🌊 <strong>物理惯性滚动</strong>：真实的减速度和惯性效果</li>
          <li>🔥 <strong>弹性边界回弹</strong>：超出边界时的自然回弹动画</li>
          <li>⚡ <strong>高性能渲染</strong>：使用GPU加速的transform动画</li>
          <li>🎨 <strong>丝滑缓动函数</strong>：采用贝塞尔曲线实现的自然缓动</li>
          <li>📊 <strong>实时滚动指示器</strong>：动态显示当前滚动位置</li>
          <li>💫 <strong>交错入场动画</strong>：柱子依次出现，视觉层次分明</li>
          <li>🎪 <strong>光泽悬停效果</strong>：鼠标悬停时的渐变光泽动画</li>
        </ul>
      </div>
    </div>
    
    <!-- 数据展示 -->
    <div class="data-display">
      <h3>当前普通图表数据</h3>
      <div class="data-grid">
        <div 
          v-for="(item, index) in chartData.series" 
          :key="index"
          class="data-item"
        >
          <h4>{{ item.name }}</h4>
          <div class="data-values">
            <span 
              v-for="(value, idx) in item.data" 
              :key="idx"
              class="value-tag"
            >
              {{ chartData.categories[idx] }}: {{ value }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import BarChart from '@/components/BarChart.vue'
import ScrollableBarChart from '@/components/ScrollableBarChart.vue'
import NewScrollableBarChart from '@/components/NewScrollableBarChart.vue'
import SmoothScrollBarChart from '@/components/SmoothScrollBarChart.vue'
import { generateBarChartData, generateScrollableBarData, generateSingleBarData, generateNewScrollableBarData } from '@/mock/chartData'
import type { BarChartData, ScrollableBarItem } from '@/mock/chartData'

const chartData = ref<BarChartData>({
  title: '',
  categories: [],
  series: []
})

const scrollableData = ref<BarChartData>({
  title: '',
  categories: [],
  series: []
})

const singleBarData = ref<BarChartData>({
  title: '',
  categories: [],
  series: []
})

const newScrollableData = ref<ScrollableBarItem[]>([])

// 为新的丝滑组件准备数据
const smoothScrollData = ref<Array<{label: string, value: number, color?: string}>>([])

const isLoading = ref(false)
const isScrollLoading = ref(false)
const isSingleLoading = ref(false)
const isNewScrollLoading = ref(false)

/**
 * 刷新普通图表数据
 * <AUTHOR>
 * @date 2025-08-18 16:46:09
 */
const refreshData = async () => {
  isLoading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 500))
  
  chartData.value = generateBarChartData()
  isLoading.value = false
}

/**
 * 刷新滑动图表数据
 */
const refreshScrollableData = async () => {
  isScrollLoading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 800))
  
  scrollableData.value = generateScrollableBarData()
  isScrollLoading.value = false
}

/**
 * 刷新单柱图表数据
 * <AUTHOR>
 * @date 2025-08-18 16:58:42
 */
const refreshSingleBarData = async () => {
  isSingleLoading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 600))
  
  singleBarData.value = generateSingleBarData()
  isSingleLoading.value = false
}

/**
 * 刷新新的滑动图表数据
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const refreshNewScrollableData = async () => {
  isNewScrollLoading.value = true
  
  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  newScrollableData.value = generateNewScrollableBarData()
  
  // 同时更新丝滑组件的数据
  smoothScrollData.value = newScrollableData.value.map(item => ({
    label: item.label,
    value: item.value,
    color: item.color
  }))
  
  isNewScrollLoading.value = false
}

onMounted(() => {
  refreshData()
  refreshScrollableData()
  refreshSingleBarData()
  refreshNewScrollableData()
})
</script>

<style scoped>
.chart-view {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 30px;
}

.chart-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 20px 15px 15px;
  padding-left: 10px;
  border-left: 4px solid #667eea;
}

.chart-container.scrollable {
  margin: 15px;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
}

.chart-container.custom-scrollable {
  margin: 15px;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.05), rgba(56, 249, 215, 0.05));
  box-shadow: 0 8px 30px rgba(67, 233, 123, 0.15);
  border: 1px solid rgba(67, 233, 123, 0.1);
}

.chart-container.single-bar {
  margin: 15px;
  padding: 20px;
  border-radius: 15px;
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.05), rgba(0, 242, 254, 0.05));
  box-shadow: 0 8px 30px rgba(79, 172, 254, 0.15);
  border: 1px solid rgba(79, 172, 254, 0.1);
}

.action-bar {
  padding: 15px;
  text-align: center;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 30px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
  font-weight: 600;
}

.refresh-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px 0 rgba(240, 147, 251, 0.4);
}

.refresh-btn.single-bar-btn {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 15px 0 rgba(79, 172, 254, 0.4);
}

.refresh-btn.custom {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  box-shadow: 0 4px 15px 0 rgba(67, 233, 123, 0.4);
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.6);
}

.refresh-btn.secondary:hover {
  box-shadow: 0 6px 20px 0 rgba(240, 147, 251, 0.6);
}

.refresh-btn.single-bar-btn:hover {
  box-shadow: 0 6px 20px 0 rgba(79, 172, 254, 0.6);
}

.refresh-btn.custom:hover {
  box-shadow: 0 6px 20px 0 rgba(67, 233, 123, 0.6);
}

.refresh-btn:active {
  transform: translateY(0);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.instruction-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  margin: 15px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.instruction-card h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.instruction-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.instruction-card li {
  padding: 8px 0;
  color: #555;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.instruction-card strong {
  color: #667eea;
  font-weight: 600;
}

.feature-card {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.05));
  margin: 15px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(79, 172, 254, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.feature-card h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.feature-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-card li {
  padding: 8px 0;
  color: #555;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-card strong {
  color: #4facfe;
  font-weight: 600;
}

.feature-card.custom {
  background: linear-gradient(135deg, rgba(67, 233, 123, 0.1), rgba(56, 249, 215, 0.05));
  box-shadow: 0 4px 20px rgba(67, 233, 123, 0.1);
  border: 1px solid rgba(67, 233, 123, 0.2);
}

.feature-card.custom strong {
  color: #43e97b;
  font-weight: 600;
}

.chart-container.ultimate-scrollable {
  margin: 15px;
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08), rgba(139, 92, 246, 0.05));
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.2);
  border: 2px solid rgba(99, 102, 241, 0.15);
  position: relative;
}

.chart-container.ultimate-scrollable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
  z-index: 1;
}

.refresh-btn.ultimate {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 4px 15px 0 rgba(99, 102, 241, 0.4);
  position: relative;
  overflow: hidden;
}

.refresh-btn.ultimate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.refresh-btn.ultimate:hover {
  box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.6);
}

.refresh-btn.ultimate:hover::before {
  left: 100%;
}

.feature-card.ultimate {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.05));
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
  border: 1px solid rgba(99, 102, 241, 0.25);
  position: relative;
  overflow: hidden;
}

.feature-card.ultimate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

.feature-card.ultimate strong {
  color: #6366f1;
  font-weight: 700;
}

.data-display {
  background: rgba(255, 255, 255, 0.9);
  margin: 15px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.data-display h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.data-item h4 {
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.data-values {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.value-tag {
  background: linear-gradient(135deg, #f0f2f5, #e8eaed);
  color: #666;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.value-tag:hover {
  background: linear-gradient(135deg, #e8eaed, #f0f2f5);
  transform: translateY(-1px);
}

@media screen and (max-width: 768px) {
  .chart-container {
    margin: 10px;
    padding: 15px;
  }
  
  .chart-container.scrollable {
    margin: 10px;
  }
  
  .chart-container.custom-scrollable {
    margin: 10px;
  }
  
  .chart-container.ultimate-scrollable {
    margin: 10px;
  }
  
  .data-display,
  .instruction-card {
    margin: 10px;
    padding: 15px;
  }
  
  .refresh-btn {
    padding: 10px 25px;
    font-size: 14px;
  }
  
  .section-title {
    font-size: 16px;
    margin: 15px 10px 10px;
  }
  
  .instruction-card li {
    font-size: 13px;
  }
}
</style>