<template>
  <div class="chart-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">终极丝滑柱状图演示</h1>
    </div>

    <!-- 全新丝滑柱状图（手势交互+回弹效果） -->
    <div class="chart-section">
      <h2 class="section-title">🚀 终极丝滑柱状图（手势交互+回弹效果）</h2>
      <div class="chart-container ultimate-scrollable">
        <SmoothScrollBarChart
          :data="smoothScrollData"
          title="全国城市经济数据排行榜"
          :bar-width="55"
          :bar-spacing="18"
          :max-bar-height="200"
        />
      </div>

      <!-- 刷新按钮 -->
      <div class="action-bar">
        <button class="refresh-btn ultimate" @click="refreshSmoothScrollData" :disabled="isSmoothScrollLoading">
          {{ isSmoothScrollLoading ? '数据生成中...' : '重新生成数据' }}
        </button>
      </div>

      <!-- 终极特性说明 -->
      <div class="feature-card ultimate">
        <h4>🏆 终极特性展示</h4>
        <ul>
          <li>🎯 <strong>精准手势识别</strong>：支持触摸、鼠标、滚轮多种交互方式</li>
          <li>🌊 <strong>物理惯性滚动</strong>：真实的减速度和惯性效果</li>
          <li>🔥 <strong>弹性边界回弹</strong>：超出边界时的自然回弹动画</li>
          <li>⚡ <strong>高性能渲染</strong>：使用GPU加速的transform动画</li>
          <li>🎨 <strong>丝滑缓动函数</strong>：采用贝塞尔曲线实现的自然缓动</li>
          <li>📊 <strong>实时滚动指示器</strong>：动态显示当前滚动位置</li>
          <li>💫 <strong>交错入场动画</strong>：柱子依次出现，视觉层次分明</li>
          <li>🎪 <strong>光泽悬停效果</strong>：鼠标悬停时的渐变光泽动画</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import SmoothScrollBarChart from '@/components/SmoothScrollBarChart.vue'
import { generateNewScrollableBarData } from '@/mock/chartData'
import type { ScrollableBarItem } from '@/mock/chartData'

// 为丝滑组件准备数据
const smoothScrollData = ref<Array<{label: string, value: number, color?: string}>>([])

const isSmoothScrollLoading = ref(false)

/**
 * 刷新丝滑柱状图数据
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
const refreshSmoothScrollData = async () => {
  isSmoothScrollLoading.value = true

  // 模拟异步加载
  await new Promise(resolve => setTimeout(resolve, 1000))

  const newScrollableData: ScrollableBarItem[] = generateNewScrollableBarData()

  // 更新丝滑组件的数据
  smoothScrollData.value = newScrollableData.map(item => ({
    label: item.label,
    value: item.value,
    color: item.color
  }))

  isSmoothScrollLoading.value = false
}

onMounted(() => {
  refreshSmoothScrollData()
})
</script>

<style scoped>
.chart-view {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 30px;
}

.page-header {
  text-align: center;
  padding: 20px 15px;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.chart-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 20px 15px 15px;
  padding-left: 10px;
  border-left: 4px solid #667eea;
}

.action-bar {
  padding: 15px;
  text-align: center;
}

.refresh-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 12px 30px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px 0 rgba(102, 126, 234, 0.4);
  font-weight: 600;
}

.refresh-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(102, 126, 234, 0.6);
}

.refresh-btn:active {
  transform: translateY(0);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.feature-card {
  background: linear-gradient(135deg, rgba(79, 172, 254, 0.1), rgba(0, 242, 254, 0.05));
  margin: 15px;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(79, 172, 254, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(79, 172, 254, 0.2);
}

.feature-card h4 {
  margin-bottom: 12px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.feature-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-card li {
  padding: 8px 0;
  color: #555;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-card strong {
  color: #4facfe;
  font-weight: 600;
}

.chart-container.ultimate-scrollable {
  margin: 15px;
  padding: 0;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08), rgba(139, 92, 246, 0.05));
  box-shadow: 0 10px 40px rgba(99, 102, 241, 0.2);
  border: 2px solid rgba(99, 102, 241, 0.15);
  position: relative;
}

.chart-container.ultimate-scrollable::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
  z-index: 1;
}

.refresh-btn.ultimate {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 4px 15px 0 rgba(99, 102, 241, 0.4);
  position: relative;
  overflow: hidden;
}

.refresh-btn.ultimate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.refresh-btn.ultimate:hover {
  box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.6);
}

.refresh-btn.ultimate:hover::before {
  left: 100%;
}

.feature-card.ultimate {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.05));
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.15);
  border: 1px solid rgba(99, 102, 241, 0.25);
  position: relative;
  overflow: hidden;
}

.feature-card.ultimate::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
}

.feature-card.ultimate strong {
  color: #6366f1;
  font-weight: 700;
}

@media screen and (max-width: 768px) {
  .chart-container.ultimate-scrollable {
    margin: 10px;
  }

  .feature-card {
    margin: 10px;
    padding: 15px;
  }

  .refresh-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  .section-title {
    font-size: 16px;
    margin: 15px 10px 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .feature-card li {
    font-size: 13px;
  }
}
</style>