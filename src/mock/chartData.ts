/**
 * 图表数据mock工具类
 * <AUTHOR>
 * @date 2025-08-18 16:32:00
 */

export interface ChartDataItem {
  name: string
  value: number
  color?: string
}

export interface BarChartData {
  title: string
  categories: string[]
  series: {
    name: string
    data: number[]
  }[]
}

/**
 * 生成随机数
 */
const randomNum = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

/**
 * 生成柱状图模拟数据
 */
export const generateBarChartData = (): BarChartData => {
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  
  return {
    title: '2024年销售数据统计',
    categories: months,
    series: [
      {
        name: '销售额(万元)',
        data: months.map(() => randomNum(50, 300))
      },
      {
        name: '订单数量',
        data: months.map(() => randomNum(20, 150))
      }
    ]
  }
}

/**
 * 生成简单柱状图数据
 */
export const generateSimpleBarData = (): ChartDataItem[] => {
  const categories = ['产品A', '产品B', '产品C', '产品D', '产品E']
  const colors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
  
  return categories.map((name, index) => ({
    name,
    value: randomNum(10, 100),
    color: colors[index]
  }))
}

/**
 * 生成可滑动柱状图数据
 * <AUTHOR>
 * @date 2025-08-18 16:46:09
 */
export const generateScrollableBarData = (): BarChartData => {
  // 生成更多数据以支持滑动，使用季度数据展示
  const categories = [
    '2023 Q1', '2023 Q2', '2023 Q3', '2023 Q4',
    '2024 Q1', '2024 Q2', '2024 Q3', '2024 Q4',
    '2025 Q1', '2025 Q2', '2025 Q3', '2025 Q4',
    '华东区', '华南区', '华北区', '华中区', '西南区', '东北区'
  ]
  
  return {
    title: '销售额数据滑动展示',
    categories,
    series: [
      {
        name: '销售额(万元)',
        data: categories.map(() => randomNum(80, 500))
      }
    ]
  }
}

/**
 * 生成最近3年每月数据的柱状图
 * <AUTHOR>
 * @date 2025-08-18 17:04:19
 */
export const generateSingleBarData = (): BarChartData => {
  const currentYear = new Date().getFullYear()
  const years = [currentYear - 2, currentYear - 1, currentYear] // 最近3年
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
  
  // 生成3年x12个月=36个类别
  const categories: string[] = []
  years.forEach(year => {
    months.forEach(month => {
      categories.push(`${year}年${month}`)
    })
  })
  
  return {
    title: `${years[0]}-${years[2]}年月度销售数据`,
    categories,
    series: [
      {
        name: '销售额(万元)',
        data: categories.map(() => randomNum(100, 600))
      }
    ]
  }
}

/**
 * 新柱状图组件数据项接口
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
export interface ScrollableBarItem {
  label: string
  value: number
  color?: string
}

/**
 * 生成新的可滑动柱状图数据（支持横向滑动和回弹动画）
 * <AUTHOR>
 * @date 2025-08-18 17:21:34
 */
export const generateNewScrollableBarData = (): ScrollableBarItem[] => {
  const categories = [
    '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都',
    '武汉', '重庆', '西安', '天津', '青岛', '大连', '厦门', '宁波',
    '无锡', '福州', '济南', '长沙', '哈尔滨', '沈阳', '郑州', '石家庄',
    '太原', '昆明', '南昌', '贵阳', '南宁', '兰州'
  ]
  
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', 
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1',
    '#14b8a6', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16'
  ]
  
  return categories.map((label, index) => ({
    label,
    value: randomNum(150, 800),
    color: colors[index % colors.length]
  }))
}