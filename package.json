{"name": "h5-vue-echarts", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "echarts": "^5.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.10", "typescript": "~5.3.0", "vue-tsc": "^1.8.25", "@types/node": "^20.10.6", "prettier": "^3.1.1", "eslint": "^8.56.0", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint-plugin-vue": "^9.19.2"}}